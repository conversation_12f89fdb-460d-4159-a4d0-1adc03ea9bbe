'use client';

import { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RiSearchLine, RiFilterLine, RiUserLine, RiSidebarUnfoldLine, RiSidebarFoldLine } from '@remixicon/react';
import { useCustomersList } from '@/hooks/use-customers';
import { DraggableCustomerItem } from './draggable-customer-item';
import type { CustomerResponse } from '@/lib/validations';
import { cn } from '@/lib/utils';

interface CustomerSidebarProps {
  className?: string;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function CustomerSidebar({ className, collapsed = false, onToggleCollapse }: CustomerSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showOnlyWithCredits, setShowOnlyWithCredits] = useState(false);

  // Fetch customers with search
  const { customers, loading, error } = useCustomersList({
    search: searchQuery,
    limit: 100, // Get more customers for the sidebar
  });

  // Filter customers based on credits filter
  const filteredCustomers = useMemo(() => {
    if (!showOnlyWithCredits) return customers;
    return customers.filter((customer) => customer.sessionCredits > 0);
  }, [customers, showOnlyWithCredits]);

  // Group customers by credit status
  const { withCredits, withoutCredits } = useMemo(() => {
    const withCredits: CustomerResponse[] = [];
    const withoutCredits: CustomerResponse[] = [];

    filteredCustomers.forEach((customer) => {
      if (customer.sessionCredits > 0) {
        withCredits.push(customer);
      } else {
        withoutCredits.push(customer);
      }
    });

    return { withCredits, withoutCredits };
  }, [filteredCustomers]);

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>Error loading customers</p>
            <p className="text-sm">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Collapsed state - show minimal sidebar
  if (collapsed) {
    return (
      <Card className={cn("h-full border-r shadow-sm", className)}>
        <CardContent className="p-2 h-full flex flex-col items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="w-8 h-8 p-0 mb-2"
            title="Expand customer sidebar"
          >
            <RiSidebarUnfoldLine className="h-4 w-4" />
          </Button>
          <div className="flex-1 flex items-center">
            <RiUserLine className="h-5 w-5 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("h-full border-r shadow-sm", className)}>
      <CardHeader className="pb-3 flex-shrink-0">
        <CardTitle className="text-lg flex items-center gap-2 justify-between">
          <div className="flex items-center gap-2">
            <RiUserLine className="h-5 w-5" />
            Customers
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="w-8 h-8 p-0"
            title="Collapse customer sidebar"
          >
            <RiSidebarFoldLine className="h-4 w-4" />
          </Button>
        </CardTitle>
        <CardDescription>Drag customers to schedule them for workouts</CardDescription>
      </CardHeader>

      <CardContent className="flex flex-col h-full space-y-4 flex-1 min-h-0">
        {/* Search */}
        <div className="relative flex-shrink-0">
          <RiSearchLine className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search customers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filter */}
        <div className="flex items-center gap-2 flex-shrink-0">
          <Button
            variant={showOnlyWithCredits ? 'default' : 'outline'}
            size="sm"
            onClick={() => setShowOnlyWithCredits(!showOnlyWithCredits)}
            className="flex items-center gap-1"
          >
            <RiFilterLine className="h-3 w-3" />
            Credits only
          </Button>
          <Badge variant="secondary" className="text-xs">
            {filteredCustomers.length} customers
          </Badge>
        </div>

        {/* Customer List */}
        <div className="flex-1 min-h-0">
          <ScrollArea className="h-full -mx-6 px-6 pb-2">
          <div className="space-y-2">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 5 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 flex-1">
                        <Skeleton className="h-4 w-4 rounded-full" />
                        <div className="space-y-1 flex-1">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-3 w-32" />
                        </div>
                      </div>
                      <Skeleton className="h-5 w-8" />
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <>
                {/* Customers with credits */}
                {withCredits.length > 0 && (
                  <>
                    {withCredits.map((customer) => (
                      <DraggableCustomerItem key={customer.id} customer={customer} />
                    ))}
                  </>
                )}

                {/* Customers without credits */}
                {withoutCredits.length > 0 && !showOnlyWithCredits && (
                  <>
                    {withCredits.length > 0 && (
                      <div className="py-2">
                        <div className="text-xs text-muted-foreground font-medium">No Credits</div>
                      </div>
                    )}
                    {withoutCredits.map((customer) => (
                      <DraggableCustomerItem key={customer.id} customer={customer} canDrag={false} />
                    ))}
                  </>
                )}

                {/* Empty state */}
                {filteredCustomers.length === 0 && !loading && (
                  <div className="text-center py-8 text-muted-foreground">
                    <RiUserLine className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">{searchQuery ? 'No customers found' : 'No customers available'}</p>
                  </div>
                )}
              </>
            )}
          </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
}
