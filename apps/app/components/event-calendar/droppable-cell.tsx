'use client';

import { useDroppable } from '@dnd-kit/core';

import { cn } from '@/lib/utils';
import { useCalendarDnd } from '@/components/event-calendar';

interface DroppableCellProps {
  id: string;
  date: Date;
  time?: number; // For week/day views, represents hours (e.g., 9.25 for 9:15)
  children?: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export function DroppableCell({ id, date, time, children, className, onClick }: DroppableCellProps) {
  const { activeEvent, activeCustomer, dragType } = useCalendarDnd();

  const { setNodeRef, isOver } = useDroppable({
    id,
    data: {
      date,
      time,
    },
  });

  // Format time for display in tooltip (only for debugging)
  const formattedTime =
    time !== undefined
      ? `${Math.floor(time)}:${Math.round((time - Math.floor(time)) * 60)
          .toString()
          .padStart(2, '0')}`
      : null;

  // Determine if we should show drop feedback
  const showEventDropFeedback = isOver && activeEvent && dragType === 'event';
  const showCustomerDropFeedback = isOver && activeCustomer && dragType === 'customer';
  const canDropCustomer = activeCustomer && activeCustomer.sessionCredits > 0;

  return (
    <div
      ref={setNodeRef}
      onClick={onClick}
      className={cn(
        'data-dragging:bg-accent flex h-full flex-col px-0.5 py-1 sm:px-1 relative transition-colors duration-200',
        'hover:bg-accent cursor-pointer',
        showEventDropFeedback && 'bg-primary/10 ring-2 ring-primary/20',
        showCustomerDropFeedback && canDropCustomer && 'bg-green-50 ring-2 ring-green-200',
        showCustomerDropFeedback && !canDropCustomer && 'bg-red-50 ring-2 ring-red-200',
        className
      )}
      title={formattedTime ? `${formattedTime} - Click to create workout` : 'Click to create workout'}
      data-dragging={isOver && (activeEvent || activeCustomer) ? true : undefined}
    >
      {children}

      {/* Customer drop feedback */}
      {showCustomerDropFeedback && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-10">
          <div
            className={cn(
              'text-xs font-medium px-2 py-1 rounded shadow-sm',
              canDropCustomer ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            )}
          >
            {canDropCustomer ? 'Drop to schedule' : 'No credits'}
          </div>
        </div>
      )}
    </div>
  );
}
